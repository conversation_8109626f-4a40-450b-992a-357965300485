/**
 * 小红书签名生成器 - 生产环境版本
 * 基于完全逆向的 launcher.js 实现
 */

const crypto = require('crypto');

class XiaohongshuSigner {
  constructor(options = {}) {
    // 浏览器环境参数 - 可以通过options自定义
    this.browserParams = {
      webId: options.webId || "962be56945c60c73dcfc50af5e20ab12",
      gid: options.gid || "yYijdddD2dSfyYijdddDyKVijKMjIydyMx6jKfqKJF62ITq88MY2k0888yYqj228Jyy4Yf2J",
      userId: options.userId || "60892496000000000101e4f3",
      a1: options.a1 || "196fca1b06btx3ck2bgkh6u4433khsrfjvgycbd0d30000295085",
      xsecappid: options.xsecappid || "ratlin",
      ...options.browserParams
    };

    // localStorage 参数 - 可以通过options自定义
    this.localStorage = {
      b1b1: options.b1b1 || "1",
      p1: options.p1 || "88",
      b1: options.b1 || "I38rHdgsjopgIvesdVwgIC+oIELmBZ5e3VwXLgFTIxS3bqwErFeexd0ekncAzMFYnqthIhJeSfMDKutRI3KsYorWHPtGrbV0P9WfIi/eWc6eYqtyQApPI37ekmR6QL+5Ii6sdneeSfqYHqwl2qt5B0DBIx++GDi/sVtkIxdsxuwr4qtiIhuaIE3e3LV0I3VTIC7e0utl2ADmsLveDSKsSPw5IEvsiVtJOqw8BuwfPpdeTFWOIx4TIiu6ZPwrPut5IvlaLbgs3qtxIxes1VwHIkumIkIyejgsY/WTge7eSqte/D7sDcpipedeYrDtIC6eDVw2IENsSqtlnlSuNjVtIvoekqt3cZ7sVo4gIESyIhEgNnquIxhnqz8gIkIfoqwkICqWJ73sdlOeVPw3IvAe0fgedfWNIi5s3IPM2utAIiKsidvekZNeTPt4nAOeWPwEIvk/aAvefuw5gfesSuweI3TrIxE5Luwwaqw+reibqrOeYjgskqtgIkdeYg0exWbxIhgsfMes6jclIkAe3PtTIirdQqwJ8ut9I36e3PtVIiNe1PtlIi5efVwAHutMGqwxI3QUICEeJaPAGl/siqtMIhVtIieeYuwLrYQrLa6sdjbiIiryqPtwmuwSIvAe6VtuQc7eTY5siF8LIEJsVfOe3qtpLPwqIvvefVwiIk6ekoLwIvKeYBAeWVw0Iv3e3ut7wsusIipJIx5e3Z6sijdsDIgeDqwnIhF5Ivc6cVwtIxrAIiNeTczEy95ekPw/IiWaLuwSOrAe1eNsVutCJVtRIkhoICpVOqtVBuwFIi7siU6ejuwI4qtiIvml+ut4IiZCwaPmIkvs0dos07Kekuw8mqtgIiPmIx5sVutWIEKejrpFIvYpeITGIvbiIvoeiVwSzutNPutdI3hVIh0s6j5eSVwIgFViIvPnJVteoutQIxFWIxEyIi+iIhdeTMesSMkUtuwgICrUcVwOeuw9IEve3gFTIirlKoNejVtlBPtcomIgZ7VWIkmkLPwTIkOsil+LIizprPwdIi8PHPt5+utIICuoIE+JICpQBqwIIkcmICNsdqwmOjm/ICFMIhds3gvs1uwSIv8BIELvLlW3IC4uaWqWIC5s0MvejPwxIEKsVVtLIhuNIC5sfqwGIvbYBqwXIvvsiYPZaPt1Nut6IhKejVtgIE5sSFMjIhJs1LAs1VtQQAOsSqwiBFOsTutaIxNs6UAeWPtyIELjIiQbsPw7+ZboIxOeDVwNraos3ZH=",
      ...options.localStorage
    };
  }

  /**
   * UTF-8编码函数
   */
  utf8Encode(str) {
    str = str.replace(/\r\n/g, "\n");
    let result = "";
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      if (code < 128) {
        result += String.fromCharCode(code);
      } else if (code > 127 && code < 2048) {
        result += String.fromCharCode((code >> 6) | 192);
        result += String.fromCharCode(((code >> 6) & 63) | 128);
      } else {
        result += String.fromCharCode((code >> 12) | 224);
        result += String.fromCharCode(((code >> 6) & 63) | 128);
        result += String.fromCharCode((63 & code) | 128);
      }
    }
    return result;
  }

  /**
   * 自定义Base64编码
   */
  customBase64Encode(str) {
    const chars = "A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3";
    let result = "";
    let i = 0;
    
    const input = this.utf8Encode(str);
    
    while (i < input.length) {
      const a = input.charCodeAt(i++);
      const b = input.charCodeAt(i++);
      const c = input.charCodeAt(i++);
      
      const bitmap = (a << 16) | (b << 8) | c;
      
      result += chars.charAt((bitmap >> 18) & 63);
      result += chars.charAt((bitmap >> 12) & 63);
      result += chars.charAt(isNaN(b) ? 64 : (bitmap >> 6) & 63);
      result += chars.charAt(isNaN(c) ? 64 : bitmap & 63);
    }
    
    return result;
  }

  /**
   * 构建完整URL
   */
  buildFullURL(apiPath, params) {
    if (!params || Object.keys(params).length === 0) {
      return apiPath;
    }
    
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, value);
    });
    
    const fullUrl = `${apiPath}?${searchParams.toString()}`;
    return fullUrl.replace(/'/g, "%27");
  }

  /**
   * 生成X-S-Common头部
   */
  generateXSCommon() {
    const m = {
      s0: 3,
      s1: "",
      x0: this.localStorage.b1b1,
      x1: "4.1.4",
      x2: "PC",
      x3: "creativity-center",
      x4: "1.31.5",
      x5: this.localStorage.p1,
      x6: "",
      x7: "",
      x8: this.localStorage.b1,
      x9: "",
      x10: 0,
      x11: "lite",
    };

    const jsonStr = JSON.stringify(m);
    return this.customBase64Encode(jsonStr);
  }

  /**
   * 生成签名
   * @param {string} apiPath - API路径，如 "/api/solar/kol/data_v2/notes_detail"
   * @param {object} params - 请求参数对象
   * @param {number} timestamp - 可选的时间戳，不传则使用当前时间
   * @returns {object} 包含 X-s, X-t 的签名对象
   */
  generateSignature(apiPath, params = {}, timestamp = null) {
    const ts = timestamp || Date.now();
    const env = "test"; // 浏览器环境标识
    
    // 构建完整URL
    const fullUrl = this.buildFullURL(apiPath, params);
    
    // 构建签名字符串：[时间戳][环境][完整URL][空字符串]
    const signString = [ts, env, fullUrl, ""].join("");
    
    // 生成MD5哈希
    const md5Hash = crypto.createHash('md5').update(signString, 'utf8').digest('hex');
    
    // 自定义Base64编码
    const signature = this.customBase64Encode(md5Hash);
    
    return {
      "X-s": signature,
      "X-t": ts
    };
  }

  /**
   * 生成完整的请求头
   * @param {string} apiPath - API路径
   * @param {object} params - 请求参数
   * @param {string} cookie - Cookie字符串
   * @param {object} extraHeaders - 额外的请求头
   * @returns {object} 完整的请求头对象
   */
  generateHeaders(apiPath, params = {}, cookie = "", extraHeaders = {}) {
    const signature = this.generateSignature(apiPath, params);
    const xsCommon = this.generateXSCommon();
    
    return {
      "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Accept": "application/json, text/plain, */*",
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      "Accept-Encoding": "gzip, deflate, br",
      "Referer": "https://pgy.xiaohongshu.com/",
      "Origin": "https://pgy.xiaohongshu.com",
      "Connection": "keep-alive",
      "Sec-Fetch-Dest": "empty",
      "Sec-Fetch-Mode": "cors",
      "Sec-Fetch-Site": "same-origin",
      "Host": "pgy.xiaohongshu.com",
      "Cookie": cookie,
      "x-s": signature["X-s"],
      "x-t": signature["X-t"].toString(),
      "X-S-Common": xsCommon,
      "f": "1",
      ...extraHeaders
    };
  }

  /**
   * 更新浏览器参数
   * @param {object} newParams - 新的浏览器参数
   */
  updateBrowserParams(newParams) {
    this.browserParams = { ...this.browserParams, ...newParams };
  }

  /**
   * 更新localStorage参数
   * @param {object} newParams - 新的localStorage参数
   */
  updateLocalStorage(newParams) {
    this.localStorage = { ...this.localStorage, ...newParams };
  }
}

module.exports = { XiaohongshuSigner };
