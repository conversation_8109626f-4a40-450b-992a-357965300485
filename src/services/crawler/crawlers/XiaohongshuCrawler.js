/**
 * 小红书爬虫
 * 基于真实API接口的小红书平台爬虫实现
 */

const axios = require('axios');
const BaseCrawler = require('./BaseCrawler');
const CookieManager = require('../../CookieManager');
const AuthorVideoService = require('../../AuthorVideoService');
const xiaoh<PERSON>shu_singer = require('./xiaoh<PERSON><PERSON>_singer.js');

class XiaohongshuCrawler extends BaseCrawler {
  constructor() {
    super('xiaohongshu');

    // Cookie管理器
    this.cookieManager = new CookieManager();
    this.currentCookie = null;

    // 防爬虫配置
    this.antiCrawlerConfig = {
      minDelay: 2000,
      maxDelay: 5000,
      maxRetries: 3,
      retryDelay: 3000,
      requestTimeout: 15000
    };

    // 用户代理池
    this.userAgents = [
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
    ];

    this.baseUrl = 'https://pgy.xiaohongshu.com';
    this.isRunning = false;
  }

  /**
   * 初始化爬虫
   */
  async initialize() {
    console.log('📱 小红书爬虫初始化...');
    // 获取可用的Cookie
    await this.refreshCookie();
  }

  /**
   * 获取或刷新Cookie
   */
  async refreshCookie() {
    try {
      this.currentCookie = await this.cookieManager.getAvailableCookie('xiaohongshu');
      if (this.currentCookie) {
        console.log(`🍪 获取到可用Cookie: ${this.currentCookie.accountName}`);
      } else {
        console.error('❌ 没有可用的小红书Cookie，无法执行爬取任务');
        throw new Error('没有可用的小红书Cookie，请先添加有效的Cookie后再执行爬取任务');
      }
    } catch (error) {
      console.error('获取Cookie失败:', error.message);
      this.currentCookie = null;
      throw error;
    }
  }

  /**
   * 执行爬取任务
   * @param {Object} config 爬取配置
   * @param {Object} callbacks 回调函数
   */
  async crawl(config, callbacks = {}) {
    const taskId = config.crawlTaskId;
    this.isRunning = true;

    // 如果有任务ID，记录任务开始
    if (taskId) {
      this.markTaskStart(taskId, config);
    }

    const results = {
      totalCount: 0,
      successCount: 0,
      failedCount: 0,
      data: []
    };

    try {
      console.log(`🚀 开始爬取小红书数据，关键词: ${config.keywords}`);

      // 首先检查是否有可用的Cookie
      await this.refreshCookie();
      if (!this.currentCookie) {
        throw new Error('没有可用的小红书Cookie，无法执行爬取任务');
      }

      // 分页爬取（支持断点续传）
      const startPage = config.startPage || 1;
      console.log(`🔄 从第 ${startPage} 页开始爬取（共 ${config.maxPages} 页）`);

      for (
        let page = startPage;
        page <= config.maxPages && this.isRunning && (!taskId || this.activeTasks.has(taskId));
        page++
      ) {
        try {
          console.log(`📄 正在爬取第 ${page} 页...`);

          // 更新进度
          const progress = {
            currentPage: page,
            totalPages: config.maxPages,
            percentage: Math.round(((page - 1) / config.maxPages) * 100),
            successCount: results.successCount,
            failedCount: results.failedCount
          };

          if (callbacks.onProgress) {
            await callbacks.onProgress(progress);
          }

          // 获取达人列表
          const pageData = await this.fetchAuthorList(
            config.keywords,
            page,
            config.pageSize || 20,
            config.searchType,
            config
          );

          if (!pageData || !pageData.authors || pageData.authors.length === 0) {
            console.log(`📄 第 ${page} 页无数据，停止爬取`);
            break;
          }

          console.log(`📄 第 ${page} 页获取到 ${pageData.authors.length} 个达人`);
          results.totalCount += pageData.authors.length;

          // 批量处理达人详细信息
          // 调试日志：检查minFirstNotePlayCount参数传递
          console.log(`🔍 [crawl] 第${page}页 - 检查minFirstNotePlayCount参数:`);
          console.log(`   原始config.minFirstNotePlayCount: ${config.minFirstNotePlayCount}`);
          console.log(`   类型: ${typeof config.minFirstNotePlayCount}`);

          const crawlOptions = {
            saveVideos: config.saveVideos !== false, // 默认为true，除非明确设置为false
            crawlTaskId: config.crawlTaskId || null,
            minFirstNotePlayCount:
              config.minFirstNotePlayCount !== undefined && config.minFirstNotePlayCount !== null
                ? config.minFirstNotePlayCount
                : 1 // 修复：只有undefined或null时才使用默认值
          };

          console.log(`   最终crawlOptions.minFirstNotePlayCount: ${crawlOptions.minFirstNotePlayCount}`);

          await this.processAuthorsInBatches(pageData.authors, results, callbacks, crawlOptions);

          // 页面间延迟
          if (page < config.maxPages && this.isRunning && (!taskId || this.activeTasks.has(taskId))) {
            const pageDelay = this.getRandomDelay(3000, 6000);
            console.log(`⏳ 页面间等待 ${pageDelay}ms...`);
            await this.delay(pageDelay);
          }
        } catch (error) {
          console.error(`❌ 第 ${page} 页爬取失败:`, error.message);
          results.failedCount++;

          if (callbacks.onError) {
            await callbacks.onError(error);
          }
        }
      }

      // 最终进度更新
      if (callbacks.onProgress) {
        await callbacks.onProgress({
          currentPage: config.maxPages,
          totalPages: config.maxPages,
          percentage: 100,
          successCount: results.successCount,
          failedCount: results.failedCount
        });
      }

      console.log(`✅ 小红书爬取完成，成功: ${results.successCount}, 失败: ${results.failedCount}`);
      return results;
    } catch (error) {
      console.error('❌ 小红书爬取失败:', error.message);
      throw error;
    } finally {
      this.isRunning = false;
      // 如果有任务ID，记录任务结束
      if (taskId) {
        this.markTaskEnd(taskId);
      }
    }
  }

  /**
   * 获取达人列表
   * @param {string} keyword 搜索关键词
   * @param {number} page 页码
   * @param {number} pageSize 每页数量
   */
  async fetchAuthorList(keyword, page, pageSize = 20, searchType = 1, transConfig) {
    const url = `${this.baseUrl}/api/solar/cooperator/blogger/v2`;

    // 使用 buildSearchData 方法构建完整的搜索参数
    const requestData = this.buildSearchData({
      ...transConfig,
      keyword: keyword,
      pageNum: page,
      pageSize: pageSize,
      searchType: searchType
    });

    const config = this.createRequestConfig('post', url, requestData);
    const response = await this.fetchWithRetry(url, config);

    if (!response || !response.data || !response.data.kols) {
      console.warn(`⚠️ 第 ${page} 页达人列表获取失败或无数据`);
      return { authors: [] };
    }

    console.log(`📄 第 ${page} 页API返回 ${response.data.kols.length} 个达人数据`);

    // 转换数据格式，映射小红书API字段到标准结构
    const authors = response.data.kols
      .map(kol => {
        try {
          return this.transformKolData(kol);
        } catch (error) {
          console.error(`❌ 转换达人数据失败 (userId: ${kol.userId}):`, error.message);
          return null;
        }
      })
      .filter(author => author !== null); // 过滤掉转换失败的数据

    console.log(`✅ 第 ${page} 页成功转换 ${authors.length} 个达人数据`);
    return { authors };
  }

  /**
   * 转换小红书API达人数据到标准格式
   * @param {Object} kol 小红书API返回的达人对象
   */
  transformKolData(kol) {
    if (!kol || !kol.userId) {
      throw new Error('达人数据缺少必要的userId字段');
    }

    // 尝试多个可能的播放量中位数字段
    let playMid = null;
    if (kol.clickMidNum !== undefined && kol.clickMidNum !== null) {
      playMid = kol.clickMidNum;
    } else if (kol.readMidNum !== undefined && kol.readMidNum !== null) {
      playMid = kol.readMidNum;
    } else if (kol.playMidNum !== undefined && kol.playMidNum !== null) {
      playMid = kol.playMidNum;
    } else if (kol.interMidNum !== undefined && kol.interMidNum !== null) {
      playMid = kol.interMidNum;
    }

    // 基础信息映射
    const transformedData = {
      userId: kol.userId,
      nickname: kol.name || '暂无昵称',
      avatar: kol.headPhoto || null,
      fansCount: kol.fansNum || 0,
      city: kol.location || '暂无',
      playMid: playMid,

      // 价格信息
      priceInfo: {
        picturePrice: kol.picturePrice || null,
        videoPrice: kol.videoPrice || null,
        lowerPrice: kol.lowerPrice || null
      },

      // 统计数据
      statsInfo: {
        businessNoteCount: kol.businessNoteCount || 0,
        clickMidNum: kol.clickMidNum || 0,
        interMidNum: kol.interMidNum || 0,
        totalNoteCount: kol.totalNoteCount || 0,
        readMidNum: kol.readMidNum || 0,
        playMidNum: kol.playMidNum || 0
      },

      // 标签信息
      tagsInfo: {
        personalTags: this.parseTagsArray(kol.personalTags),
        contentTags: this.parseTagsArray(kol.contentTags),
        featureTags: this.parseTagsArray(kol.featureTags)
      },

      // 保留完整的原始数据
      rawData: kol
    };

    return transformedData;
  }

  /**
   * 解析标签数组
   * @param {Array|string} tags 标签数据
   */
  parseTagsArray(tags) {
    if (!tags) return [];

    if (Array.isArray(tags)) {
      return tags.filter(tag => tag && typeof tag === 'string');
    }

    if (typeof tags === 'string') {
      try {
        const parsed = JSON.parse(tags);
        return Array.isArray(parsed) ? parsed : [tags];
      } catch {
        return [tags];
      }
    }

    return [];
  }

  /**
   * 批量处理达人信息
   * @param {Array} authors 达人列表
   * @param {Object} results 结果对象
   * @param {Object} callbacks 回调函数
   * @param {Object} options 选项配置
   * @param {number} options.minFirstNotePlayCount 首个关联帖子最低播放量过滤阈值，默认为1
   */
  async processAuthorsInBatches(authors, results, callbacks, options = {}) {
    const batchSize = 1; // 每批只处理1个达人，避免数据错乱
    const taskId = options.crawlTaskId;
    // 修复：只有undefined或null时才使用默认值，避免0被重置为1
    const minFirstNotePlayCount =
      options.minFirstNotePlayCount !== undefined && options.minFirstNotePlayCount !== null
        ? options.minFirstNotePlayCount
        : 1;

    console.log(`🎯 首个帖子播放量过滤阈值: ${minFirstNotePlayCount}`);
    console.log(`🔍 [processAuthorsInBatches] 参数检查:`);
    console.log(`   options.minFirstNotePlayCount: ${options.minFirstNotePlayCount}`);
    console.log(`   类型: ${typeof options.minFirstNotePlayCount}`);
    console.log(`   最终使用值: ${minFirstNotePlayCount}`);

    for (let i = 0; i < authors.length && this.isRunning && (!taskId || this.activeTasks.has(taskId)); i += batchSize) {
      const author = authors[i]; // 直接取单个达人
      console.log(`🔄 处理达人 ${i + 1}/${authors.length}: ${author.userId}`);

      try {
        // 首先进行首个帖子播放量检查
        const firstNotePlayCountCheck = await this.checkFirstNotePlayCount(author.rawData, minFirstNotePlayCount);

        if (!firstNotePlayCountCheck.passed) {
          console.log(`⚠️ 达人 ${author.userId} 首个帖子播放量不满足条件，跳过处理`);
          console.log(
            `   首个帖子播放量: ${firstNotePlayCountCheck.firstNotePlayCount}, 要求阈值: ${minFirstNotePlayCount}`
          );
          results.failedCount++;

          // 达人间延迟（即使跳过也要延迟）
          if (i + 1 < authors.length && this.isRunning && (!taskId || this.activeTasks.has(taskId))) {
            const delay = this.getRandomDelay(1000, 2000); // 跳过的达人延迟时间较短
            console.log(`⏳ 达人间等待 ${delay}ms...`);
            await this.delay(delay);
          }
          continue; // 跳过当前达人，继续处理下一个
        }

        console.log(`✅ 达人 ${author.userId} 首个帖子播放量检查通过: ${firstNotePlayCountCheck.firstNotePlayCount}`);

        const detail = await this.getAuthorDetail(author.userId, author, options);

        if (detail && detail.nickname && detail.nickname !== '暂无') {
          results.successCount++;
          results.data.push(detail);

          if (callbacks.onResult) {
            await callbacks.onResult(detail);
          }

          // 显示更详细的成功信息
          const priceInfo = author.priceInfo?.videoPrice || author.priceInfo?.lowerPrice || '未知';
          const fansCount = this.formatNumber(detail.followersCount);
          console.log(
            `✅ 达人 ${author.userId} (${detail.nickname}) 信息获取成功 - 粉丝: ${fansCount}, 报价: ${priceInfo}`
          );
        } else {
          console.log(`⚠️ 达人 ${author.userId} 信息不完整，跳过`);
          results.failedCount++;
        }
      } catch (error) {
        console.error(`❌ 获取达人 ${author.userId} 信息失败:`, error.message);
        results.failedCount++;

        if (callbacks.onError) {
          await callbacks.onError(error);
        }
      }

      // 达人间延迟
      if (i + 1 < authors.length && this.isRunning && (!taskId || this.activeTasks.has(taskId))) {
        const delay = this.getRandomDelay(3000, 5000);
        console.log(`⏳ 达人间等待 ${delay}ms...`);
        await this.delay(delay);
      }
    }
  }

  /**
   * 获取达人详细信息
   * @param {string} authorId 达人ID
   * @param {Object} author 原始达人对象
   * @param {Object} options 选项配置
   */
  async getAuthorDetail(authorId, author, options = {}) {
    try {
      // 获取达人笔记数据
      const notesInfo = await this.getAuthorAllNotes(authorId);

      // 构建完整的达人信息对象，使用API返回的丰富数据
      const authorDetail = {
        platform: 'xiaohongshu',
        platformUserId: authorId,
        nickname: author.nickname || '暂无',
        avatarUrl: author.avatar || null,
        followersCount: this.parseFollowerCount(author.fansCount),
        city: author.city || '暂无',
        uniqueId: authorId, // 小红书使用userId作为唯一标识
        playMid: author.playMid !== undefined && author.playMid !== null ? author.playMid : null,
        contactInfo: {
          wechat: null, // 小红书API通常不直接提供联系方式
          phone: null
        },
        videoStats: {
          videoCount: notesInfo.noteCount || author.statsInfo?.totalNoteCount || 0,
          averagePlay: notesInfo.averagePlay || 0,
          totalPlay: notesInfo.totalPlay || 0,
          totalLike: notesInfo.totalLike || 0,
          totalComment: notesInfo.totalComment || 0,
          totalShare: notesInfo.totalShare || 0,
          // 添加小红书特有的统计数据
          businessNoteCount: author.statsInfo?.businessNoteCount || 0,
          clickMidNum: author.statsInfo?.clickMidNum || 0,
          interMidNum: author.statsInfo?.interMidNum || 0
        },
        rawData: {
          authorInfo: author.rawData,
          notesInfo,
          // 保留转换后的结构化数据
          transformedData: {
            priceInfo: author.priceInfo,
            statsInfo: author.statsInfo,
            tagsInfo: author.tagsInfo
          }
        },
        // 达人扩展信息，使用小红书API的真实数据
        authorExtInfo: this.extractAuthorExtInfoFromKol(author)
      };

      // 如果启用了视频保存功能，则保存笔记数据到数据库
      if (options.saveVideos && options.crawlTaskId && notesInfo.notes && notesInfo.notes.length > 0) {
        try {
          console.log(`📝 开始保存达人 ${authorId} 的笔记数据到数据库...`);

          const videoSaveResult = await AuthorVideoService.saveVideosFromCrawlTask(
            authorDetail,
            options.crawlTaskId,
            notesInfo.notes
          );

          console.log(`✅ 达人 ${authorId} 笔记保存结果:`, {
            total: videoSaveResult.total,
            success: videoSaveResult.success,
            created: videoSaveResult.created,
            updated: videoSaveResult.updated,
            failed: videoSaveResult.failed
          });

          // 将保存结果添加到返回数据中
          authorDetail.videoSaveResult = videoSaveResult;
        } catch (videoSaveError) {
          console.error(`❌ 保存达人 ${authorId} 笔记数据失败:`, videoSaveError.message);
          // 不影响主流程，只记录错误
          authorDetail.videoSaveError = videoSaveError.message;
        }
      }

      return authorDetail;
    } catch (error) {
      console.error(`❌ 获取达人 ${authorId} 详细信息失败:`, error.message);
      return null;
    }
  }

  /**
   * 检查达人首个帖子播放量是否满足条件
   * @param {string} authorId 达人列表信息
   * @param {number} minPlayCount 最低播放量要求
   * @returns {Object} 检查结果 {passed: boolean, firstNotePlayCount: number, noteId: string}
   */
  async checkFirstNotePlayCount(author, minPlayCount) {
    try {
      console.log(`🔍 检查达人 ${author.name}(${author.userId}) 首个帖子播放量，要求阈值: ${minPlayCount}`);
      const authorId = author.userId;

      // 获取达人笔记数据
      const noteList = author.noteList;

      if (!noteList || noteList.length === 0) {
        console.log(`⚠️ 达人 ${authorId} 没有笔记数据`);
        return {
          passed: false,
          firstNotePlayCount: 0,
          noteId: null,
          reason: '没有笔记数据'
        };
      }

      // 获取第一篇帖子
      const firstNote = noteList[0];
      const firstNoteId = firstNote.noteId;

      if (!firstNoteId) {
        console.log(`⚠️ 达人 ${authorId} 首个帖子缺少noteId`);
        return {
          passed: false,
          firstNotePlayCount: 0,
          noteId: null,
          reason: '首个帖子缺少noteId'
        };
      }

      // 调用小红书API获取首个帖子的详细播放量
      const noteDetail = await this.fetchXiaohongshuNoteDetail(firstNoteId);

      if (!noteDetail) {
        console.log(`⚠️ 达人 ${authorId} 首个帖子详情获取失败`);
        return {
          passed: false,
          firstNotePlayCount: firstNote.playCount || 0, // 使用备用数据
          noteId: firstNoteId,
          reason: '帖子详情获取失败，使用备用数据'
        };
      }

      // 从详细数据中获取播放量（readNum字段）
      const actualPlayCount = noteDetail.readNum || 0;

      const passed = actualPlayCount >= minPlayCount;

      console.log(`📊 达人 ${authorId} 首个帖子播放量检查: ${actualPlayCount} ${passed ? '≥' : '<'} ${minPlayCount}`);

      return {
        passed,
        firstNotePlayCount: actualPlayCount,
        noteId: firstNoteId,
        reason: passed ? '检查通过' : '播放量不足'
      };
    } catch (error) {
      console.error(`❌ 检查达人 ${authorId} 首个帖子播放量失败:`, error.message);
      return {
        passed: false,
        firstNotePlayCount: 0,
        noteId: null,
        reason: `检查失败: ${error.message}`
      };
    }
  }

  /**
   * 调用小红书API获取单条帖子详情
   * @param {string} noteId 帖子ID
   * @returns {Object} 帖子详情数据
   */
  async fetchXiaohongshuNoteDetail(noteId) {
    try {
      const url = `${this.baseUrl}/api/solar/note/${noteId}/detail`;
      const config = this.createRequestConfig('get', url);
      const response = await this.fetchWithRetry(url, config);

      if (!response || !response.data) {
        console.warn(`⚠️ 帖子 ${noteId} 详情获取失败`);
        return null;
      }

      return response.data;
    } catch (error) {
      console.error(`❌ 获取帖子 ${noteId} 详情失败:`, error.message);
      return null;
    }
  }

  /**
   * 【外部用】获取达人卡片信息 给快速提报用
   * @param {string} authorId 达人ID
   */
  async getAuthorCardInfo(authorId) {
    try {
      const url = `${this.baseUrl}/api/solar/cooperator/user/blogger/${authorId}`;
      const config = this.createRequestConfig('get', `${url}`);
      const response = await this.fetchWithRetry(url, config);

      if (!response || !response.data) {
        console.warn(`⚠️ 达人 ${authorId} 达人卡片数据获取失败`);
        return {};
      }
      // 输出完整的响应数据用于调试
      console.log('🔍 小红书达人卡片API响应数据结构:', JSON.stringify(response.data, null, 2));

      // 保存入库
      const authorCardData = response.data;

      return response.data;
    } catch (error) {
      console.error(`获取达人 ${authorId} 主页概览数据失败:`, error.message);
      return {};
    }
  }

  /**
   * 获取达人主页概览信息 [判断水号用] 拿到pagePercentVo
   * @param {string} authorId 达人ID
   */
  async getAuthorPageInfo(authorId) {
    try {
      const url = `${this.baseUrl}/api/solar/kol/data_v3/notes_rate`;
      const config = this.createRequestConfig('get', `${url}?userId=${authorId}`);
      const response = await this.fetchWithRetry(url, config);

      if (!response || !response.data) {
        console.warn(`⚠️ 达人 ${authorId} 主页概览数据获取失败`);
        return { noteCount: 0, notes: [] };
      }

      // 输出完整的响应数据用于调试
      console.log('🔍 小红书API响应数据结构:', JSON.stringify(response.data, null, 2));

      // 解析笔记数据
      const pagePercentVo = response.data.pagePercentVo;

      if (!pagePercentVo) {
        console.warn(`⚠️ 响应中没有找到 pagePercentVo 字段`);
        console.log('📋 可用字段:', Object.keys(response.data));
      }

      return {
        pagePercentVo: pagePercentVo,
        rawResponse: response.data // 保留原始响应用于调试
      };
    } catch (error) {
      console.error(`获取达人 ${authorId} 主页概览数据失败:`, error.message);
      return {
        pagePercentVo: {
          impHomefeedPercent: 0,
          impSearchPercent: 0,
          impFollowPercent: 0,
          impDetailPercent: 0,
          impNearbyPercent: 0,
          impOtherPercent: 0,
          readHomefeedPercent: 0,
          readSearchPercent: 0,
          readFollowPercent: 0,
          readDetailPercent: 0,
          readNearbyPercent: 0,
          readOtherPercent: 0
        }
        // ...可后续拓展
      };
    }
  }

  /**
   * 获取达人关键词概览信息 + 获取达人全部笔记数据
   * @param {string} authorId 达人ID
   */
  async getAuthorAllNotes(authorId) {
    try {
      const url = `${this.baseUrl}/api/solar/kol/data_v2/notes_detail?advertiseSwitch=1&orderType=1&pageNumber=1&pageSize=1000&userId=${authorId}&noteType=4&isThirdPlatform=0`;
      const config = this.createRequestConfig('get', `${url}`);
      const response = await this.fetchWithRetry(url, config);

      if (!response || !response.data) {
        console.warn(`⚠️ 达人 ${authorId} 笔记数据获取失败`);
        return { noteCount: 0, notes: [] };
      }

      // 解析笔记数据
      const notesData = response.data;
      const notes = this.parseNotesData(notesData);

      // 计算统计数据
      const totalPlay = notes.reduce((sum, note) => sum + (note.readNum || 0), 0);
      const totalLike = notes.reduce((sum, note) => sum + (note.likeNum || 0), 0);
      const totalComment = notes.reduce((sum, note) => sum + (note.commentCount || 0), 0);
      const totalShare = notes.reduce((sum, note) => sum + (note.shareCount || 0), 0);
      const averagePlay = notes.length > 0 ? Math.round(totalPlay / notes.length) : 0;

      return {
        noteCount: notes.length,
        averagePlay,
        totalPlay,
        totalLike,
        totalComment,
        totalShare,
        notes
      };
    } catch (error) {
      console.error(`获取达人 ${authorId} 笔记信息失败:`, error.message);
      return { noteCount: 0, notes: [] };
    }
  }

  /**
   * 解析笔记数据
   * @param {Object} notesData 原始笔记数据
   */
  parseNotesData(notesData) {
    if (!notesData || !Array.isArray(notesData.list)) {
      return [];
    }

    return notesData.list.map(note => {
      const noteId = note.noteId || note.id;
      return {
        videoId: noteId,
        title: note.title || '无标题',
        playCount: note.readNum || 0,
        likeCount: note.likeNum || 0,
        commentCount: note.interactionNum || 0,
        shareCount: note.shareCount || 0,
        collectCount: note.collectNum || 0, // 添加收藏量
        publishTime: note.date,
        duration: note.duration || 0,
        videoUrl: noteId ? `https://www.xiaohongshu.com/explore/${noteId}` : null, // 生成小红书观看链接
        videoCover: note.imgUrl || note.coverUrl,
        description: note.description || note.content,
        rawData: note
      };
    });
  }

  /**
   * 解析粉丝数量
   * @param {string|number} followerStr 粉丝数字符串或数字
   */
  parseFollowerCount(followerStr) {
    if (!followerStr && followerStr !== 0) return 0;

    // 如果已经是数字，直接返回
    if (typeof followerStr === 'number') {
      return Math.round(followerStr);
    }

    const str = String(followerStr).toLowerCase().trim();

    // 处理空字符串
    if (!str) return 0;

    // 提取数字部分
    const numMatch = str.match(/[\d.]+/);
    if (!numMatch) return 0;

    const num = parseFloat(numMatch[0]);
    if (isNaN(num)) return 0;

    // 处理单位
    if (str.includes('万') || str.includes('w')) {
      return Math.round(num * 10000);
    } else if (str.includes('k')) {
      return Math.round(num * 1000);
    } else if (str.includes('m')) {
      return Math.round(num * 1000000);
    } else {
      return Math.round(num);
    }
  }

  /**
   * 从小红书KOL数据中提取达人扩展信息
   * @param {Object} author 转换后的达人对象
   */
  extractAuthorExtInfoFromKol(author) {
    if (!author) return {};

    const extInfo = {
      // 标签信息
      tags_relation: [],
      content_theme_labels_180d: [],

      // 价格信息
      price_info: {
        picture_price: author.priceInfo?.picturePrice || null,
        video_price: author.priceInfo?.videoPrice || null,
        lower_price: author.priceInfo?.lowerPrice || null
      },

      // 统计信息
      stats_info: {
        business_note_count: author.statsInfo?.businessNoteCount || 0,
        click_mid_num: author.statsInfo?.clickMidNum || 0,
        inter_mid_num: author.statsInfo?.interMidNum || 0,
        total_note_count: author.statsInfo?.totalNoteCount || 0
      },

      // 标签分类
      personal_tags: author.tagsInfo?.personalTags || [],
      content_tags: author.tagsInfo?.contentTags || [],
      feature_tags: author.tagsInfo?.featureTags || []
    };

    // 合并所有标签到tags_relation（保持与星图爬虫的兼容性）
    const allTags = [...(author.tagsInfo?.personalTags || []), ...(author.tagsInfo?.featureTags || [])];
    extInfo.tags_relation = [...new Set(allTags)]; // 去重

    // 内容标签作为内容主题
    extInfo.content_theme_labels_180d = author.tagsInfo?.contentTags || [];

    // 兼容性字段（与星图爬虫保持一致）
    extInfo.price_20_60 = author.priceInfo?.videoPrice || author.priceInfo?.lowerPrice || null;

    return extInfo;
  }

  /**
   * 提取达人扩展信息（兼容旧方法）
   * @param {Object} detailInfo 详情信息
   * @deprecated 使用 extractAuthorExtInfoFromKol 替代
   */
  extractAuthorExtInfo(detailInfo) {
    if (!detailInfo) return {};

    return {
      tags_relation: detailInfo.tags || [],
      content_theme_labels_180d: detailInfo.contentThemes || [],
      price_20_60: detailInfo.price || null
    };
  }

  /**
   * 构建搜索达人的请求参数
   * @param {Object} options 搜索选项
   * @param {string} options.keyword 搜索关键词
   * @param {number} options.pageNum 页码，默认为1
   * @param {number} options.pageSize 每页数量，默认为20
   * @param {string} options.brandUserId 品牌用户ID，可选
   * @param {string} options.column 排序字段，默认为'comprehensiverank'
   * @param {string} options.sort 排序方式，默认为'desc'
   * @param {number} options.searchType 搜索类型，默认为1
   * @returns {Object} 完整的搜索请求体
   */
  buildSearchData(options = {}) {
    // 设置默认值
    const { keyword = '', pageNum = 1, pageSize = 20, searchType = 1 } = options;

    // 构建完整的搜索请求体
    const searchData = {
      searchType: searchType,
      keyword: keyword,
      pageNum: pageNum,
      pageSize: pageSize,
      column: 'comprehensiverank',
      sort: 'desc',
      brandUserId: '5eba0c54000000000101d9f9',
      marketTarget: null,
      audienceGroup: [],
      personalTags: [],
      gender: null,
      location: null,
      signed: -1,
      featureTags: [],
      fansAge: 0,
      fansGender: 0,
      accumCommonImpMedinNum30d: [],
      readMidNor30: [],
      interMidNor30: [],
      thousandLikePercent30: [],
      noteType: 0,
      progressOrderCnt: [],
      tradeType: '不限',
      tradeReportBrandIdSet: [],
      excludedTradeReportBrandId: false,
      estimateCpuv30d: [],
      firstIndustry: '',
      secondIndustry: '',
      newHighQuality: 0,
      filterIntention: false,
      flagList: [
        {
          flagType: 'HAS_BRAND_COOP_BUYER_AUTH',
          flagValue: '0'
        },
        {
          flagType: 'IS_HIGH_QUALITY',
          flagValue: '0'
        }
      ],
      activityCodes: [],
      excludeLowActive: false,
      fansNumUp: 0,
      excludedTradeReportBrand: false,
      excludedTradeInviteReportBrand: false
    };

    console.log(`📋 构建小红书搜索参数: 关键词="${keyword}", 页码=${pageNum}, 页面大小=${pageSize}`);

    return searchData;
  }

  /**
   * 创建请求配置
   * @param {string} method 请求方法
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   */
  createRequestConfig(method, url, data = null) {
    // 使用Cookie中的User-Agent或随机选择
    const userAgent = this.currentCookie?.userAgent || this.getRandomUserAgent();
    // 请求需要签名 生产x-s和x-t、x-s-common
    

    const config = {
      method,
      url,
      headers: {
        'User-Agent': userAgent,
        Accept: 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        Connection: 'keep-alive',
        Accept: 'application/json, text/plain, */*',
        Referer: this.baseUrl,
        Origin: this.baseUrl,
        Priority: 'u=1, i',
        'x-s': '0gZkZjOv1lML0g4UOYwksi1CZ2aUsYqJ0gOv0gcCsBF3',
        'x-s-common':
          '2UQAPsHCPUIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahFHjIj2eHjwjQ+GnPW/MPjNsQhPUHCHdQY4BlkJjMAyBpVJsHVHdWFH0ijPshIN0D7PsHVHdWMH0ijP/D98f+YPnHI+fQF2e+0yAQj89Ti+dLF+ePAy9YAqf8x4f4EG9QDPBcAPeZIPeHE+/Zh+aHVHdW9H0ijHjIj2eqjwjHjNsQhwsHCHDDAwoQH8B4AyfRI8FS98g+Dpd4daLP3JFSb/BMsn0pSPM87nrldzSzQ2bPAGdb7zgQB8nph8emSy9E0cgk+zSS1qgzianYt8p+f/LzN4gzaa/+NqMS6qS4HLozoqfQnPbZEp98QyaRSp9P98pSl4oSzcgmca/P78nTTL08z/sVManD9q9z18np/8db8aob7JeQl4epsPrzsagW3tF4ryaRApdz3agYDq7YM47HFqgzkanYMGLSbP9LA/bGIa/+nprSe+9LI4gzVPDbrJg+P4fprLFTALMm7+LSb4d+kpdzt/7b7wrQM498cqBzSpr8g/FSh+bzQygL9nSm7qSmM4epQ4flY/BQdqA+l4oYQ2BpAPp87arS34nMQyFSE8nkdqMD6pMzd8/4SL7bF8aRr+7+rG7mkqBpD8pSUzozQcA8Szb87PDSb/d+/qgzVJfl/4LExpdzQ4fRSy7bFP9+y+7+nJAzdaLp/2LSizL4z8dbMagYiJdbCwB4QyFSfJ7b7yFSeqp4t+A+A8BlO8p8c4A+Q4DbSPB8d8nzfpFEQy/pAPFSc//QM4rbQyLTAynz98nTy/fpLLocFJDbO8p4c4FpQ4fV6GLr78n8l4MYIJDbAzob78LDAwoQQ2rLM/op749bl4UTU8nSjqgQO8pSx87+3qgzdanTD8pSdPBphp9QhanYdq98+8gP9yf+VanTm8/+c4bzQygQDLgb7a0YM4eSQPA8SPMmFpDSk/fLlLozVanDM8n8n4FbH4gz+z7b72rDALppQcFpSafbccL4VN7+kqgz+anYn4rSk8np84g41nSk34rQp+g+gcpbranTo/rkl4rTYqg4Mag8D8/8n4oYzqrTSPFSm8/bP2dQQzLkAyFQO8p8l49QTLozdagWAq9zl4eSQzLEApf8INFDAa7+gpLRAPob74DSePBLALozMwsRoqLS3LpkQP9pSPpSmqA+m+7PIp9zS8op7pDSiJemQcFqAJdb7qFShyLTQPAmS8Sp8wokjP9LlpdqIa/+cPobl49QAcfpS2bpmqA+c4BDU4gclanTl/DS9tAD3pdc78gb7aDSh8o+DGA8S+Sm7qgmn4BMQyecMJ7bFLDShndQScgQ0anT9qFcEPo+f8FRSpS874bbl4sRQ2BWFa/P9qMzc4b+QyrkSyfQ8prSezgksnpSiag8IGDSe+9pLpd4xGM8F2pmM4r+QPMmQanYOq7Ym/fLl4g4e8F8ncFSbzBqhqgzjnS8F8LShanSQ2BlzanT3yLSi89pnzDEAprMbaUTM49pQcFq72Sm7GnRn49lQzaRSynEgyDS3z9TNJ94SzobFP7Qn4oS62f+IqDQopLS3t94lLo4aa/PIqMSxnn4Qy/cUGS87nLSkwb8yLozNab8FpFS9LsTQzLLFanYTa9pM4FzQyL8GaLp9q9Tn4M4TnfMkaLpMqDSe87+xJFEA2ob7JrS9JLQQzLYx/oko4DS9pb+PG9YTag8SqM4U8Bpgpd4yaLp6q9Sn4rYQzp4DaLLIq9zM49MQcMQ0qdp7nrS94dPIJrI32S8F/LRl4B4Qye8SySmFLrSi8o+8z0zBaLP7q7Y/+gPlpdz74rbmqM4c4MpjzfpApb8F/rShN7+3pFTSPp87qDSip0FjNsQhwaHCN/rFPAZh+AWF+0GVHdWlPsHCPsIj2erlH0ijJBSF8aQR',
        'x-t': '1753846530746'
      },
      timeout: this.antiCrawlerConfig.requestTimeout
    };

    // 添加Cookie
    // console.log(`currentCookie: ${JSON.stringify(this.currentCookie)}`);
    if (this.currentCookie?.cookieString) {
      config.headers['Cookie'] = this.currentCookie.cookieString;
      console.log(`🍪 使用Cookie: ${this.currentCookie.cookieString.substring(0, 50)}...`);
    } else {
      console.error('❌ 没有可用的Cookie，无法发起请求');
      throw new Error('没有可用的Cookie，无法发起请求');
    }

    if (method.toLowerCase() === 'post' && data) {
      config.data = data;
      // console.log(`📤 请求数据: ${JSON.stringify(data, null, 2)}`);
    }

    return config;
  }

  /**
   * 带重试的请求方法
   * @param {string} url 请求URL
   * @param {Object} config 请求配置
   */
  async fetchWithRetry(url, config) {
    let lastError;
    let cookieRefreshed = false;

    for (let attempt = 1; attempt <= this.antiCrawlerConfig.maxRetries; attempt++) {
      try {
        // 随机延迟
        const delay = this.getRandomDelay(this.antiCrawlerConfig.minDelay, this.antiCrawlerConfig.maxDelay);
        await this.delay(delay);

        console.log(`🚀 发起请求: ${config.method.toUpperCase()} ${url}`);
        const response = await axios(config);
        console.log(`✅ 请求成功: ${response.status}`);

        if (response.data && response.status === 200) {
          return response.data;
        } else {
          // 检查是否是认证相关错误
          if (this.isAuthError(response.data) && !cookieRefreshed) {
            console.warn('🔄 检测到认证错误，尝试刷新Cookie...');
            await this.handleCookieError();
            cookieRefreshed = true;

            // 更新请求配置中的Cookie
            if (this.currentCookie?.cookieString) {
              config.headers['Cookie'] = this.currentCookie.cookieString;
            }

            // 重试当前请求
            attempt--; // 不计入重试次数
            continue;
          }

          throw new Error(`API返回错误: ${response.data?.status_msg || JSON.stringify(response.data) || '未知错误'}`);
        }
      } catch (error) {
        lastError = error;
        const errorDetails = error.response ? `${error.response.status} ${error.response.statusText}` : error.message;

        console.warn(`请求失败 (尝试 ${attempt}/${this.antiCrawlerConfig.maxRetries}): ${errorDetails}`);

        // 如果是网络错误且还有Cookie可用，尝试切换Cookie
        if (this.isNetworkError(error) && !cookieRefreshed && this.currentCookie) {
          console.warn('🔄 网络错误，尝试切换Cookie...');
          await this.refreshCookie();
          cookieRefreshed = true;

          if (this.currentCookie?.cookieString) {
            config.headers['Cookie'] = this.currentCookie.cookieString;
            config.headers['User-Agent'] = this.currentCookie.userAgent || this.getRandomUserAgent();
          }

          attempt--; // 不计入重试次数
          continue;
        }

        if (attempt < this.antiCrawlerConfig.maxRetries) {
          const retryDelay = this.antiCrawlerConfig.retryDelay * attempt;
          console.log(`⏳ ${retryDelay}ms 后重试...`);
          await this.delay(retryDelay);
        }
      }
    }

    throw lastError;
  }

  /**
   * 检查是否是认证错误
   * @param {Object} responseData 响应数据
   */
  isAuthError(responseData) {
    const authErrorCodes = [401, 403, 10001, 10002]; // 常见的认证错误码
    return (
      authErrorCodes.includes(responseData?.status_code) ||
      (responseData?.status_msg && responseData.status_msg.includes('登录'))
    );
  }

  /**
   * 检查是否是网络错误
   * @param {Error} error 错误对象
   */
  isNetworkError(error) {
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT' ||
      error.message.includes('timeout') ||
      error.message.includes('Network Error')
    );
  }

  /**
   * 处理Cookie错误
   */
  async handleCookieError() {
    if (this.currentCookie) {
      // 标记当前Cookie为过期
      await this.cookieManager.markCookieAsExpired(this.currentCookie.id, '请求返回认证错误');
    }

    // 获取新的Cookie
    await this.refreshCookie();
  }

  /**
   * 格式化数字显示
   * @param {number} num 数字
   */
  formatNumber(num) {
    if (!num || num === 0) return '0';

    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    } else {
      return num.toString();
    }
  }

  /**
   * 获取随机用户代理
   */
  getRandomUserAgent() {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  /**
   * 停止爬虫
   * @param {number} taskId 任务ID（可选）
   */
  async stop(taskId = null) {
    // 调用父类的stop方法来处理activeTasks管理
    await super.stop(taskId);

    if (taskId) {
      // 停止特定任务
      console.log(`⏹️ 小红书爬虫任务 ${taskId} 已停止`);
    } else {
      // 停止所有任务
      this.isRunning = false;
      console.log('⏹️ 小红书爬虫已停止');
    }
  }
}

module.exports = XiaohongshuCrawler;
